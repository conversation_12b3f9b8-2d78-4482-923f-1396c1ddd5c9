#!/usr/bin/env python3
"""
Preprocess CSV files for diffusion model training.

This script implements the three-part preprocessing system:
1. Process individual CSV files to add precomputed moments and conditioning features
2. Combine all processed files into a single shuffled dataset
3. Prepare for training without on-the-fly computation
"""

import os
import pandas as pd
from pathlib import Path
from src.reconstruction.diffusion import DiffusionConfig
from src.reconstruction.diffusion.data_utils import preprocess_csv_file


def process_csv_files(csv_files, config, moment_feature="pT", extra_conditioners=None):
    """
    Process multiple CSV files to add precomputed moments and conditioning features.

    Parameters:
        csv_files: List of CSV file paths
        config: DiffusionConfig object
        moment_feature: Feature to compute moments for (default: 'pT')
        extra_conditioners: Optional callable for additional conditioning features

    Returns:
        List of output file paths
    """
    output_files = []

    print(f"Processing {len(csv_files)} CSV files...")

    for csv_file in csv_files:
        if not os.path.exists(csv_file):
            print(f"Warning: File {csv_file} not found, skipping...")
            continue

        try:
            output_path = preprocess_csv_file(
                csv_file,
                config,
                moment_feature=moment_feature,
                extra_conditioners=extra_conditioners,
            )
            output_files.append(output_path)

        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue

    print(f"Successfully processed {len(output_files)} files")
    return output_files


def combine_and_shuffle_datasets(
    input_files=None,
    output_path="diffusion_training_dataset.csv",
    max_rows=None,
    random_state=42,
):
    """
    Combine all processed CSV files into a single shuffled training dataset.

    Parameters:
        input_files: List of input CSV files (if None, auto-discover *_diffusion_input.csv)
        output_path: Path for the combined dataset
        max_rows: Optional maximum number of rows (with stratified sampling)
        random_state: Random seed for shuffling

    Returns:
        Path to the combined dataset
    """
    print("Combining and shuffling datasets...")

    # Auto-discover files if not provided
    if input_files is None:
        input_files = list(Path(".").glob("**/*_diffusion_input.csv"))
        input_files = [str(f) for f in input_files]

    if not input_files:
        raise ValueError("No *_diffusion_input.csv files found")

    print(f"Found {len(input_files)} files to combine")

    # Load and combine all files
    dataframes = []
    process_counts = {}

    for file_path in input_files:
        try:
            df = pd.read_csv(file_path)
            dataframes.append(df)

            # Count rows per process
            if "process" in df.columns:
                process_name = df["process"].iloc[0]
                process_counts[process_name] = len(df)

        except Exception as e:
            print(f"  Error loading {file_path}: {e}")
            continue

    if not dataframes:
        raise ValueError("No valid dataframes loaded")

    # Combine all dataframes
    combined_df = pd.concat(dataframes, axis=0, ignore_index=True)
    print(f"\nCombined dataset: {len(combined_df)} total rows")

    # Apply stratified downsampling if requested
    if max_rows is not None and len(combined_df) > max_rows:
        print(f"Downsampling to {max_rows} rows with stratified sampling...")

        if "process" in combined_df.columns:
            # Stratified sampling by process
            sampled_dfs = []
            processes = combined_df["process"].unique()
            rows_per_process = max_rows // len(processes)

            for process in processes:
                process_df = combined_df[combined_df["process"] == process]
                if len(process_df) > rows_per_process:
                    process_sample = process_df.sample(
                        n=rows_per_process, random_state=random_state
                    )
                else:
                    process_sample = process_df
                sampled_dfs.append(process_sample)

            combined_df = pd.concat(sampled_dfs, axis=0, ignore_index=True)
        else:
            # Simple random sampling
            combined_df = combined_df.sample(n=max_rows, random_state=random_state)

        print(f"After downsampling: {len(combined_df)} rows")

    # Shuffle the combined dataset
    combined_df = combined_df.sample(frac=1.0, random_state=random_state).reset_index(
        drop=True
    )

    # Save the final dataset
    combined_df.to_csv(output_path, index=False)

    # Print summary report
    print(f"Combined dataset saved: {output_path}")
    print(f"Total rows: {len(combined_df)}, Total columns: {len(combined_df.columns)}")

    if "process" in combined_df.columns:
        final_process_counts = combined_df["process"].value_counts().sort_index()
        print(f"Rows per process: {dict(final_process_counts)}")

    return output_path


def main():
    """Main function to run the preprocessing pipeline."""

    # Initialize configuration
    config = DiffusionConfig()

    # Define CSV files to process (using the specified data files)
    csv_files = [
        "data/hww_1M_MG5_final_truth.csv",
        "data/tt_1M_MG_final_truth.csv",
    ]

    print("DIFFUSION DATA PREPROCESSING PIPELINE")

    # Step 1: Process individual CSV files
    output_files = process_csv_files(csv_files, config)

    if not output_files:
        print("No files were successfully processed. Exiting.")
        return

    # Step 2: Combine and shuffle datasets
    final_dataset = combine_and_shuffle_datasets(
        input_files=output_files,
        max_rows=None,  # Set to a number if you want to limit dataset size
        random_state=42,
    )

    print(f"Preprocessing complete! Final dataset: {final_dataset}")


if __name__ == "__main__":
    main()
