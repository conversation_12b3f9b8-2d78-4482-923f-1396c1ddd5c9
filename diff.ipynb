{"cells": [{"cell_type": "code", "execution_count": 8, "id": "3765901d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 9, "id": "6b1f6ea3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved: outputs/diffusion/unfold_diffusion_lepqua_CT14lo.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>p_v_1_E_diffusion</th>\n", "      <th>p_v_1_x_diffusion</th>\n", "      <th>p_v_1_y_diffusion</th>\n", "      <th>p_v_1_z_diffusion</th>\n", "      <th>p_v_2_E_diffusion</th>\n", "      <th>p_v_2_x_diffusion</th>\n", "      <th>p_v_2_y_diffusion</th>\n", "      <th>p_v_2_z_diffusion</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>337.527722</td>\n", "      <td>-19.980663</td>\n", "      <td>73.312767</td>\n", "      <td>371.559769</td>\n", "      <td>558.972895</td>\n", "      <td>-38.271852</td>\n", "      <td>-72.935186</td>\n", "      <td>-709.977508</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-78.088440</td>\n", "      <td>-107.805312</td>\n", "      <td>-22.015437</td>\n", "      <td>-20.031238</td>\n", "      <td>186.691612</td>\n", "      <td>37.713543</td>\n", "      <td>-14.674749</td>\n", "      <td>-393.943280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-185.389549</td>\n", "      <td>28.187605</td>\n", "      <td>-12.398424</td>\n", "      <td>-24.300221</td>\n", "      <td>-57.997387</td>\n", "      <td>-62.081262</td>\n", "      <td>39.618466</td>\n", "      <td>38.469259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-111.197516</td>\n", "      <td>35.393041</td>\n", "      <td>-16.558494</td>\n", "      <td>9.027521</td>\n", "      <td>-76.536238</td>\n", "      <td>-37.216358</td>\n", "      <td>22.461120</td>\n", "      <td>6.082308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-55.842131</td>\n", "      <td>-16.124761</td>\n", "      <td>33.555314</td>\n", "      <td>44.995889</td>\n", "      <td>-89.194223</td>\n", "      <td>-38.054619</td>\n", "      <td>1.438311</td>\n", "      <td>-51.552933</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   p_v_1_E_diffusion  p_v_1_x_diffusion  p_v_1_y_diffusion  p_v_1_z_diffusion  \\\n", "0         337.527722         -19.980663          73.312767         371.559769   \n", "1         -78.088440        -107.805312         -22.015437         -20.031238   \n", "2        -185.389549          28.187605         -12.398424         -24.300221   \n", "3        -111.197516          35.393041         -16.558494           9.027521   \n", "4         -55.842131         -16.124761          33.555314          44.995889   \n", "\n", "   p_v_2_E_diffusion  p_v_2_x_diffusion  p_v_2_y_diffusion  p_v_2_z_diffusion  \n", "0         558.972895         -38.271852         -72.935186        -709.977508  \n", "1         186.691612          37.713543         -14.674749        -393.943280  \n", "2         -57.997387         -62.081262          39.618466          38.469259  \n", "3         -76.536238         -37.216358          22.461120           6.082308  \n", "4         -89.194223         -38.054619           1.438311         -51.552933  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["npy_path = \"outputs/diffusion/unfold_diffusion_lepqua_CT14lo.npy\"\n", "csv_path = npy_path.replace(\".npy\", \".csv\")\n", "\n", "# Load .npy file\n", "data = np.load(npy_path)\n", "\n", "# Wrap into DataFrame (adds column names if you like)\n", "n_cols = data.shape[1]\n", "columns = [f\"col_{i}\" for i in range(n_cols)]\n", "df = pd.DataFrame(data, columns=columns)\n", "df.drop(columns=[\"col_0\"], inplace=True)\n", "\n", "# Rename columns\n", "df.columns = [\n", "    \"p_v_1_E_diffusion\",\n", "    \"p_v_1_x_diffusion\",\n", "    \"p_v_1_y_diffusion\",\n", "    \"p_v_1_z_diffusion\",\n", "    \"p_v_2_E_diffusion\",\n", "    \"p_v_2_x_diffusion\",\n", "    \"p_v_2_y_diffusion\",\n", "    \"p_v_2_z_diffusion\",\n", "]\n", "\n", "# Save to CSV\n", "df.to_csv(csv_path, index=False)\n", "\n", "print(f\"Saved: {csv_path}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "99d6aa5d", "metadata": {}, "outputs": [], "source": ["# Load truth data\n", "truth_data = pd.read_csv(\"data/hww_1M_MG_final_truth_cuts.csv\")"]}, {"cell_type": "code", "execution_count": 11, "id": "fd55a0f5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot comparison of neutrinos of truth and diffusion\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "plt.hist(truth_data[\"p_v_1_E_truth\"], bins=50, alpha=0.5, label=\"Truth\", range=(-100, 100), density=True)\n", "plt.hist(df[\"p_v_1_E_diffusion\"], bins=50, alpha=0.5, label=\"Diffusion\", range=(-100, 100), density=True)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "7c61b61c", "metadata": {}, "outputs": [], "source": ["# Load ww and hww and compare\n", "ww_data = pd.read_csv(\"data/ww_1M_MG_final_truth_cuts.csv\")[:200000]\n", "hww_data = pd.read_csv(\"data/hww_1M_MG_final_truth_cuts.csv\")[:200000]"]}, {"cell_type": "code", "execution_count": 13, "id": "6c029600", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 1d hist of ww/hww\n", "plt.hist(ww_data[\"p_l_1_E_truth\"], bins=50, alpha=0.5, label=\"WW\", range=(-100, 100))\n", "plt.hist(hww_data[\"p_l_1_E_truth\"], bins=50, alpha=0.5, label=\"HWW\", range=(-100, 100))\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 14, "id": "e7c90036", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate transverse momentum for WW and HWW truth\n", "p_T_ww = np.sqrt(ww_data[\"p_l_1_x_truth\"]**2 + ww_data[\"p_l_1_y_truth\"]**2)\n", "p_T_hww = np.sqrt(hww_data[\"p_l_1_x_truth\"]**2 + hww_data[\"p_l_1_y_truth\"]**2)\n", "\n", "# Compute eta\n", "p_norm_ww1 = np.sqrt(ww_data[\"p_l_1_x_truth\"]**2 + ww_data[\"p_l_1_y_truth\"]**2 + ww_data[\"p_l_1_z_truth\"]**2)\n", "eta_ww1 = 0.5 * np.log((p_norm_ww1 + ww_data[\"p_l_1_z_truth\"]) / (p_norm_ww1 - ww_data[\"p_l_1_z_truth\"] + 1e-15))\n", "p_norm_ww2 = np.sqrt(ww_data[\"p_l_2_x_truth\"]**2 + ww_data[\"p_l_2_y_truth\"]**2 + ww_data[\"p_l_2_z_truth\"]**2)\n", "eta_ww2 = 0.5 * np.log((p_norm_ww2 + ww_data[\"p_l_2_z_truth\"]) / (p_norm_ww2 - ww_data[\"p_l_2_z_truth\"] + 1e-15))\n", "\n", "delta_eta_ww = eta_ww1 - eta_ww2\n", "\n", "p_norm_hww1 = np.sqrt(hww_data[\"p_l_1_x_truth\"]**2 + hww_data[\"p_l_1_y_truth\"]**2 + hww_data[\"p_l_1_z_truth\"]**2)\n", "eta_hww1 = 0.5 * np.log((p_norm_hww1 + hww_data[\"p_l_1_z_truth\"]) / (p_norm_hww1 - hww_data[\"p_l_1_z_truth\"] + 1e-15))\n", "p_norm_hww2 = np.sqrt(hww_data[\"p_l_2_x_truth\"]**2 + hww_data[\"p_l_2_y_truth\"]**2 + hww_data[\"p_l_2_z_truth\"]**2)\n", "eta_hww2 = 0.5 * np.log((p_norm_hww2 + hww_data[\"p_l_2_z_truth\"]) / (p_norm_hww2 - hww_data[\"p_l_2_z_truth\"] + 1e-15))\n", "delta_eta_hww = eta_hww1 - eta_hww2\n", "\n", "# Plot delta eta comparison\n", "plt.hist(delta_eta_ww, bins=50, alpha=0.5, label=\"WW\", range=(-10, 10))\n", "plt.hist(delta_eta_hww, bins=50, alpha=0.5, label=\"HWW\", range=(-10, 10))\n", "plt.xlabel(\"delta eta\")\n", "plt.legend()\n", "plt.show()\n", "\n", "# Compute phi for WW truth and HWW truth\n", "phi_ww = np.arctan2(ww_data[\"p_l_1_y_truth\"], ww_data[\"p_l_1_x_truth\"])\n", "phi_hww = np.arctan2(hww_data[\"p_l_1_y_truth\"], hww_data[\"p_l_1_x_truth\"])\n", "\n", "delta_phi_ww = np.arctan2(ww_data[\"p_l_1_y_truth\"], ww_data[\"p_l_1_x_truth\"]) - np.arctan2(ww_data[\"p_l_2_y_truth\"], ww_data[\"p_l_2_x_truth\"])\n", "delta_phi_hww = np.arctan2(hww_data[\"p_l_1_y_truth\"], hww_data[\"p_l_1_x_truth\"]) - np.arctan2(hww_data[\"p_l_2_y_truth\"], hww_data[\"p_l_2_x_truth\"])\n", "\n", "# Plot delta phi comparison\n", "plt.hist(delta_phi_ww, bins=50, alpha=0.5, label=\"WW\", range=(-np.pi, np.pi))\n", "plt.hist(delta_phi_hww, bins=50, alpha=0.5, label=\"HWW\", range=(-np.pi, np.pi))\n", "plt.xlabel(\"delta phi\")\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "# Plot p_T comparison\n", "plt.hist(p_T_ww, bins=50, alpha=0.5, label=\"WW\", range=(0, 150))\n", "plt.hist(p_T_hww, bins=50, alpha=0.5, label=\"HWW\", range=(0, 150))\n", "plt.xlabel(\"p_T lepton 1\")\n", "plt.legend()\n", "plt.show()\n", "\n", "# Plot eta comparison\n", "plt.hist(eta_ww1, bins=50, alpha=0.5, label=\"WW\", range=(-5, 5))\n", "plt.hist(eta_hww1, bins=50, alpha=0.5, label=\"HWW\", range=(-5, 5))\n", "plt.xlabel(\"eta lepton 1\")\n", "plt.legend()\n", "plt.show()\n", "\n", "# Plot phi comparison\n", "plt.hist(phi_ww, bins=50, alpha=0.5, label=\"WW\", range=(-np.pi, np.pi))\n", "plt.hist(phi_hww, bins=50, alpha=0.5, label=\"HWW\", range=(-np.pi, np.pi))\n", "plt.xlabel(\"phi lepton 1\")\n", "plt.legend()\n", "plt.show()\n", "\n", "met_ww = np.sqrt(ww_data[\"p_v_1_x_truth\"]**2 + ww_data[\"p_v_1_y_truth\"]**2)\n", "met_hww = np.sqrt(hww_data[\"p_v_1_x_truth\"]**2 + hww_data[\"p_v_1_y_truth\"]**2)\n", "\n", "# Plot met comparison\n", "plt.hist(met_ww, bins=50, alpha=0.5, label=\"WW\", range=(0, 150))\n", "plt.hist(met_hww, bins=50, alpha=0.5, label=\"HWW\", range=(0, 150))\n", "plt.xlabel(\"MET\")\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}