import numpy as np
import pandas as pd
import torch
from scipy import stats
from src.utils.lorentz_vector import LorentzVector
from src.utils.change_of_coordinates import exp_to_four_vec


def calculate_moments(values, n_moments=4, eps=1e-8):
    """
    Calculate the first n moments of pT distributions.

    Parameters:
        pt_values: Array of pT values
        n_moments: Number of moments to calculate

    Returns:
        Array of moment values
    """
    values = np.asarray(values, dtype=np.float64)
    mu = np.mean(values)
    xc = values - mu
    sigma = np.sqrt(np.mean(xc**2) + eps)
    outs = [mu, sigma]
    for i in range(2, n_moments):
        outs += [np.mean(xc / (sigma + eps)) ** i]
    return np.array(outs, dtype=np.float64)


def circ_moments(alpha, k_max=2):
    # alpha in [-pi, pi)
    a = np.asarray(alpha, dtype=np.float64)
    a = (a + np.pi) % (2 * np.pi) - np.pi  # wrap to [-pi, pi)
    a = a[np.isfinite(a)]
    if a.size == 0:
        return np.zeros(2 * k_max, dtype=np.float64)
    out = []
    for k in range(1, k_max + 1):
        out += [np.mean(np.cos(k * a)), np.mean(np.sin(k * a))]
    return np.array(out, dtype=np.float64)


def calculate_mode(data, bins=50):
    """
    Calculate the mode of a distribution using histogram.

    Parameters:
        data: Input data array
        bins: Number of bins for histogram

    Returns:
        Mode value
    """
    hist, bin_edges = np.histogram(data, bins=bins)
    max_bin_idx = np.argmax(hist)
    mode = (bin_edges[max_bin_idx] + bin_edges[max_bin_idx + 1]) / 2
    return mode


def calculate_kinematic_modes(four_vectors):
    """
    Calculate modes for kinematic variables from four-vectors.

    Parameters:
        four_vectors: Array of four-vectors [E, px, py, pz]

    Returns:
        Dictionary of mode values for different variables
    """
    E, px, py, pz = (
        four_vectors[:, 0],
        four_vectors[:, 1],
        four_vectors[:, 2],
        four_vectors[:, 3],
    )
    pt = np.sqrt(px**2 + py**2)

    modes = {
        "E": calculate_mode(E),
        "px": calculate_mode(px),
        "py": calculate_mode(py),
        "pz": calculate_mode(pz),
        "pt": calculate_mode(pt),
        "eta": calculate_mode(
            0.5
            * np.log(
                (np.sqrt(px**2 + py**2 + pz**2) + pz)
                / (np.sqrt(px**2 + py**2 + pz**2) - pz + 1e-8)
            )
        ),
        "phi": calculate_mode(np.arctan2(py, px)),
    }

    return modes


class DiffusionDataPreprocessor:
    """Data preprocessor specifically for diffusion model training."""

    def __init__(self, config):
        """
        Initialize with diffusion configuration.

        Parameters:
            config: DiffusionConfig object
        """
        self.config = config

    def convert_to_four_vectors(self, data):
        """
        Convert detector-level data to four-vector format.

        Parameters:
            data: DataFrame with detector-level information

        Returns:
            Four-vector arrays for leptons and missing momentum
        """
        # Extract lepton four-vectors (already in E, px, py, pz format)
        lep1_4vec = data[["p_l_1_E", "p_l_1_x", "p_l_1_y", "p_l_1_z"]].values
        lep2_4vec = data[["p_l_2_E", "p_l_2_x", "p_l_2_y", "p_l_2_z"]].values

        # Missing momentum (assume pz = 0 for missing momentum, calculate E from pT)
        mpx = data["mpx"].values
        mpy = data["mpy"].values
        mpt = np.sqrt(mpx**2 + mpy**2)

        # For missing momentum, we set E = pT and pz = 0 as initial approximation
        missing_4vec = np.column_stack([mpt, mpx, mpy, np.zeros_like(mpx)])

        return lep1_4vec, lep2_4vec, missing_4vec

    def calculate_conditioning_features(self, lep1_4vec, lep2_4vec, missing_4vec):
        """
        Calculate conditioning features including pT moments and angle information.

        Parameters:
            lep1_4vec, lep2_4vec, missing_4vec: Four-vector arrays

        Returns:
            Conditioning feature array
        """
        # Calculate pT for leptons
        px1, py1, pz1 = lep1_4vec[:, 1], lep1_4vec[:, 2], lep1_4vec[:, 3]
        px2, py2, pz2 = lep2_4vec[:, 1], lep2_4vec[:, 2], lep2_4vec[:, 3]
        lep1_pt = np.hypot(px1, py1)
        lep2_pt = np.hypot(px2, py2)

        # Calculate pT moments
        pt_1_moments = calculate_moments(lep1_pt, self.config.pt_conditioning_moments)
        pt_2_moments = calculate_moments(lep2_pt, self.config.pt_conditioning_moments)

        phi1 = np.arctan2(py1, px1)
        phi2 = np.arctan2(py2, px2)
        dphi = np.arctan2(np.sin(phi1 - phi2), np.cos(phi1 - phi2))

        eta1 = 0.5 * np.log(
            (np.sqrt(px1**2 + py1**2 + pz1**2) + pz1)
            / (np.sqrt(px1**2 + py1**2 + pz1**2) - pz1 + 1e-8)
        )
        eta2 = 0.5 * np.log(
            (np.sqrt(px2**2 + py2**2 + pz2**2) + pz2)
            / (np.sqrt(px2**2 + py2**2 + pz2**2) - pz2 + 1e-8)
        )
        deta = eta1 - eta2
        deta = (deta + np.pi) % (2 * np.pi) - np.pi  # wrap to [-pi, pi)

        eta_features = circ_moments(deta, self.config.eta_conditioning_moments)
        phi_features = circ_moments(dphi, self.config.phi_conditioning_moments)

        print(pt_1_moments.shape, pt_2_moments.shape, eta_features.shape, phi_features.shape)

        conditioning_features = np.concatenate(
            [pt_1_moments, pt_2_moments, eta_features, phi_features], axis=0
        )

        conditioning_features = np.repeat(
            conditioning_features[np.newaxis, :], lep1_4vec.shape[0], axis=0
        )

        return np.array(conditioning_features)

    def prepare_training_data(self, detector_data, truth_data):
        """
        Prepare training data for diffusion model.

        Parameters:
            detector_data: DataFrame with detector-level information
            truth_data: DataFrame with truth-level information

        Returns:
            X (conditioning data), y (target truth-level four-vectors)
        """
        # Convert detector data to four-vectors
        lep1_4vec, lep2_4vec, missing_4vec = self.convert_to_four_vectors(detector_data)

        # Calculate conditioning features
        conditioning_features = self.calculate_conditioning_features(
            lep1_4vec, lep2_4vec, missing_4vec
        )

        # Combine detector-level four-vectors with conditioning
        detector_features = np.concatenate(
            [
                lep1_4vec,  # 4 components
                lep2_4vec,  # 4 components
                missing_4vec,  # 4 components
            ],
            axis=1,
        )  # Total: 12 components

        # Combine with conditioning features
        X = np.concatenate([detector_features, conditioning_features], axis=1)

        # Extract truth-level neutrino four-vectors
        neutrino1_truth = truth_data[
            ["p_v_1_E_truth", "p_v_1_x_truth", "p_v_1_y_truth", "p_v_1_z_truth"]
        ].values
        neutrino2_truth = truth_data[
            ["p_v_2_E_truth", "p_v_2_x_truth", "p_v_2_y_truth", "p_v_2_z_truth"]
        ].values

        # Combine neutrino four-vectors
        y = np.concatenate([neutrino1_truth, neutrino2_truth], axis=1)

        return X, y

    def normalize_data(self, X, y):
        """
        Normalize data using configuration ranges.

        Parameters:
            X: Input features
            y: Target values

        Returns:
            Normalized X and y
        """
        # Normalize detector-level four-vectors (first 12 components of X)
        detector_norm = np.tile(
            [
                self.config.E_range,
                self.config.pT_range,
                self.config.pT_range,
                self.config.pT_range,
            ],
            3,
        )
        X_normalized = X.copy()
        X_normalized[:, :12] = X[:, :12] / detector_norm

        # Normalize conditioning features (moments and angles)
        # Moments: normalize by pT_range powers
        # Lep1 pT moments
        for i in range(self.config.pt_conditioning_moments):
            X_normalized[:, 12 + i] = X[:, 12 + i] / (self.config.pT_range ** (i + 1))

        # Lep2 pT moments
        offset = 12 + self.config.pt_conditioning_moments
        for i in range(self.config.pt_conditioning_moments):
            X_normalized[:, offset + i] = X[:, offset + i] / (
                self.config.pT_range ** (i + 1)
            )

        # Circular angle moments are already in [-1,1], just copy
        angle_start = offset + self.config.pt_conditioning_moments
        X_normalized[:, angle_start:] = X[:, angle_start:]

        y_normalized = y / self.config.norm_vec

        return X_normalized, y_normalized

    def denormalize_output(self, y_normalized):
        """
        Denormalize model output back to physical units.

        Parameters:
            y_normalized: Normalized model output

        Returns:
            Denormalized output in physical units
        """
        return y_normalized * self.config.norm_vec
